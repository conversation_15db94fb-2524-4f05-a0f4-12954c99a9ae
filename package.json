{"name": "upace-checkintool", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@ericblade/quagga2": "^0.0.14", "@reduxjs/toolkit": "^2.0.1", "axios": "^1.6.2", "date-fns": "^2.30.0", "framer-motion": "^10.16.15", "lodash.debounce": "^4.0.8", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.12.0", "react-redux": "^9.0.2", "react-router-dom": "^6.20.1", "react-select": "^5.8.0", "usehooks-ts": "^2.9.1"}, "devDependencies": {"@types/lodash.debounce": "^4.0.9", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.2.0", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.0.0"}}