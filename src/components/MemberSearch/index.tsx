import AsyncSelect from "react-select/async";
import useM<PERSON>berSearch from "../../hooks/useMemberSearch";
import type { CSSObjectWithLabel } from "react-select";

type TMemberSearchProps = {
  uniId: string;
};

function MemberSearch({ uniId }: TMemberSearchProps) {
  const { searchMembers, onMemberSelect } = useMemberSearch(uniId || "");

  return (
    <>
      <label id="member-search-label" className="sr-only">
        Search for a member by first or last name
      </label>
      <AsyncSelect
        isMulti={false}
        backspaceRemovesValue={false}
        components={{}}
        isClearable
        cacheOptions
        defaultOptions
        onChange={onMemberSelect}
        loadOptions={searchMembers}
        classNamePrefix="upace-select"
        placeholder="Search by first name / last name"
        aria-labelledby="member-search-label"
        aria-describedby="member-search-instructions"
        inputId="member-search-input"
        styles={{
          control: (base: CSSObjectWithLabel) => ({
            ...base,
            minHeight: "44px", // Minimum touch target size
            minWidth: "300px", // Prevent collapsing
          }),
          input: (base: CSSObjectWithLabel) => ({
            ...base,
            minHeight: "40px", // Ensure input area is large enough
          }),
          placeholder: (base: CSSObjectWithLabel) => ({
            ...base,
            fontSize: "1rem", // Larger text for better readability
          }),
        }}
        aria-required="true"
        aria-invalid="false"
      />
      <div id="member-search-instructions" className="sr-only">
        Type a member's first or last name to search. Use arrow keys to navigate
        results and Enter to select.
      </div>
    </>
  );
}

export default MemberSearch;
