import { Card, CardBody, CardHeader } from '@chakra-ui/react'
import { ReactNode } from 'react'

type TCardProps = {
  heading?: ReactNode | string
  body?: ReactNode | string
}

function CardComponent({
  heading,
  body
}: TCardProps) {
  return (
    <Card>
      {heading &&
        <CardHeader>
          {heading}
        </CardHeader>
      }
      {body &&
        <CardBody>
          {body}
        </CardBody>
      }
    </Card>
  )
}

export default CardComponent