import { TBreadBrumb } from "../../types/TBreadCrumbs"
import { Badge, Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@chakra-ui/react"
import { Link } from "react-router-dom"

type TBreadCrumbsProps = {
  paths: TBreadBrumb[]
}

function BreadCrumbs({
  paths
}: TBreadCrumbsProps) {
  return (
    <Breadcrumb spacing='8px' separator={'|'}>
      {paths.map((path, index) => (
        <BreadcrumbItem key={index} isCurrentPage={path.active}>
          <BreadcrumbLink
            as={Link}
            to={path.to || '#'}
            pointerEvents={path.active ? 'none' : 'auto'}
          >
            <Badge px={2} colorScheme='messenger'>{path.text}</Badge>
          </BreadcrumbLink>
        </BreadcrumbItem>
      ))}
      {/* <BreadcrumbItem>
        <BreadcrumbLink href='#'>Home</BreadcrumbLink>
      </BreadcrumbItem>

      <BreadcrumbItem>
        <BreadcrumbLink href='#'>About</BreadcrumbLink>
      </BreadcrumbItem>

      <BreadcrumbItem isCurrentPage>
        <BreadcrumbLink href='#'>Contact</BreadcrumbLink>
      </BreadcrumbItem> */}
    </Breadcrumb>
  )
}

export default BreadCrumbs