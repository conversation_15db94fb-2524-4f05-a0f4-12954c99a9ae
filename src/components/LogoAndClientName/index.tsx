import { Badge, Box, Flex, Heading, Image, Text } from "@chakra-ui/react"
import { FaLocationPin } from "react-icons/fa6";

type TLogoAndClientNameProps = {
  logo?: string
  clientName: string
  facilityName?: string
}

function LogoAndClientName({
  logo,
  clientName,
  facilityName
}: TLogoAndClientNameProps) {
  return (
    <Flex gap={5} alignItems='center'>
      {logo &&
        <Box>
          <Image w='80px' src={logo} />
        </Box>
      }
      <Box>
        <Heading fontWeight='900' fontSize='2xl' as='h4'>{clientName}</Heading>
        <Badge colorScheme='green' variant='subtle'>
          <Flex alignItems='center' gap={2}>
            <FaLocationPin fontSize="15px" /> <Text fontSize='sm'>{facilityName || 'All Facilities'}</Text>
          </Flex>
        </Badge>
      </Box>
    </Flex>
  )
}

export default LogoAndClientName