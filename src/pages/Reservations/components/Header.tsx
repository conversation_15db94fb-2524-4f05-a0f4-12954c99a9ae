import { Flex, Image, Text } from "@chakra-ui/react"
import useStartupData from "../../../hooks/useStartupData"
import { useNavigate } from "react-router-dom"

function Header() {

  const {
    defaultClientConfig,
    uniId
  } = useStartupData()

  const navigate = useNavigate()

  const logout = () => {
    navigate({
      pathname: `/${uniId}`
    })
  }

  return (
    <Flex
      justifyContent={'space-between'}
      alignItems={'center'}
      backgroundColor={'brand.customBlue'}
      paddingY={6}
      paddingX={10}
      borderRadius={'lg'}
    >
      <Image fit={'contain'} w='100px' src={defaultClientConfig?.configs?.class_checkin?.logo_url} />

      <Text
        cursor={'pointer'}
        color={'white'}
        fontWeight={900}
        onClick={logout}
      >
        LOG OUT
      </Text>
    </Flex>
  )
}

export default Header