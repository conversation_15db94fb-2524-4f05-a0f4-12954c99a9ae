import { Box, Heading, Text } from "@chakra-ui/react"
import { format } from "date-fns"
import { TUser } from "../../../types/TUser"
import { useEffect, useState } from "react"

type TSalutationAndDateProps = {
  user: TUser | null
}

function SalutationAndDate({
  user
}: TSalutationAndDateProps) {

  const [date, setDate] = useState<Date>(new Date());

  function tick() {
    setDate(new Date());
  }

  useEffect(() => {
    const timerID = setInterval(() => tick(), 30000);
  
    return function cleanup() {
      clearInterval(timerID);
    };
  }, []);

  return (
    <Box mt={10}>
      <Heading textTransform={'uppercase'} fontWeight={900} size='lg' as='h1'>
        Welcome, {user?.firstName} {user?.lastName}
      </Heading>
      <Text mt={1}>{format(date, 'MMMM do, YYY')} | {format(date, 'h:mm aaa')}</Text>
    </Box>
  )
}

export default SalutationAndDate