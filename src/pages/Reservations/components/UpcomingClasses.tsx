import { Card, CardBody, CardHeader, Flex, Heading, Stack, StackDivider, Text, useTheme } from "@chakra-ui/react"
import { TReservation, TUpcomingClass } from "../../../types/TReservation"
import ReservationCard from "./ReservationCard"
import { format } from "date-fns"
import { convertMinutesToReadable, convertTimeFormatToDate } from "../../../utils/Date"
import { getInstructorNameForUpcomingClasses, getUpcomingClassesCheckinState } from "../utils/Reservation"
import { TbCalendarCancel } from "react-icons/tb"
import useStartupData from "../../../hooks/useStartupData"

type TUpcomingClassesProps = {
  fetchState: string,
  upcoming: TUpcomingClass[]
  checkinReservation: (reservation: TReservation | TUpcomingClass, type: 'reservation' | 'upcoming') => void
}

function UpcomingClasses({
  fetchState,
  upcoming,
  checkinReservation
}: TUpcomingClassesProps) {

  const theme = useTheme()
  const { defaultClientConfig } = useStartupData();

  return (
    <Card
      boxShadow={'none'}
    >
      <CardHeader
        paddingX={0}
      >
        <Flex gap={4} alignItems='center'>
          <Heading fontWeight='800' size='lg'>Upcoming Classes</Heading>
        </Flex>
      </CardHeader>

      <CardBody
        paddingX={0}
      >
        <Stack divider={<StackDivider />} spacing='4'>
          {upcoming.length === 0 &&
            <Flex
              alignItems='center'
              justifyContent='start'
              flexDirection='row'
              gap={4}
           >
              <TbCalendarCancel color={theme?.colors?.brand?.midGray} fontSize="30px" />
              <Text color='gray.700' mt={2} fontWeight='500' size='sm'>
                There are no upcoming classes starting in the next {convertMinutesToReadable(defaultClientConfig?.configs?.class_checkin?.mins_before_class_to_show_reservation ?? 0)}.
              </Text>
           </Flex>
          }

          {upcoming.map((reservation, index) => (
            <ReservationCard
              key={index}
              image={reservation?.images[0]}
              title={reservation.name || ''}
              instructor={getInstructorNameForUpcomingClasses(reservation)}
              startTime={format(convertTimeFormatToDate(reservation.start_time), 'h:mm aaa')}
              endTime={format(convertTimeFormatToDate(reservation.end_time), 'h:mm aaa')}
              room={reservation.room_name}
              spotsAvailable={reservation.spots_available}
              checkinReservation={checkinReservation}
              reservationData={reservation}
              type="upcoming"
              //  Get the checkin button state based on conditions
              checkinButtonState={getUpcomingClassesCheckinState(reservation, fetchState)} />
          ))}
        </Stack>
      </CardBody>
    </Card>
  )
}

export default UpcomingClasses