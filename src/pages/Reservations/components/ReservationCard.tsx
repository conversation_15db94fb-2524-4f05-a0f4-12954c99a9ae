import { Box, Button, Flex, Heading, Image, Text } from '@chakra-ui/react'
import { ReactNode } from 'react'
import { TReservation, TUpcomingClass } from '../../../types/TReservation'
import { AiOutlineNumber, AiOutlineUser } from 'react-icons/ai'
import { MdOutlineRoom } from 'react-icons/md'
import { CiAlarmOn } from 'react-icons/ci'

type TReservationCardProps = {
  image: string
  title: string | ReactNode
  instructor?: string | null
  startTime?: string
  endTime?: string
  room: string
  gym?: string
  spotsAvailable?: number,
  type: 'reservation' | 'upcoming'
  reservationData: TReservation | TUpcomingClass
  checkinButtonState: { enabled: boolean, text: string }
  checkinReservation: (reservation: TReservation | TUpcomingClass, type: 'reservation' | 'upcoming') => void
}

function ReservationCard({
  image,
  title,
  instructor,
  startTime,
  endTime,
  room,
  spotsAvailable,
  type,
  reservationData,
  checkinButtonState,
  checkinReservation
}: TReservationCardProps) {

  return (
    <Flex
      gap={6}
      justifyContent='space-between'
      alignItems={{
        base: 'start',
        md: 'center'
      }}
      flexDirection={{
        base: 'column',
        md: 'row'
      }}>
      <Flex gap={6}>
        <Image
          borderRadius='lg'
          src={image}
          boxSize={{
            base: '90px',
            sm: '140px'
          }}
          objectFit='cover'
        />
        <Box>
          <Heading fontWeight='800' size='sm' textTransform='uppercase'>
            {title}
          </Heading>
          {instructor ?
            <Flex pt='2' gap={2} alignItems='center'>
              <AiOutlineUser /> <Text fontSize='sm'>{instructor}</Text>
            </Flex> : null }
          <Flex pt='2' gap={2} alignItems='center'>
            <MdOutlineRoom /> <Text fontSize='sm'>{room || '--'}</Text>
          </Flex>
          <Flex pt='2' gap={2} alignItems='center'>
            <CiAlarmOn /> <Text fontSize='sm'>{startTime} - {endTime}</Text>
          </Flex>
          {(spotsAvailable !== null && spotsAvailable !== undefined) &&
          <Flex pt='2' gap={2} alignItems='center'>
            <AiOutlineNumber />
            <Text fontSize='sm'>
              <Text as='span' fontWeight='900'>{spotsAvailable}</Text> spots available
            </Text>
          </Flex>
          }
        </Box>
      </Flex>
      <Box width={{ base: 'full', md: 'auto' }}>
        <Button
          onClick={() => checkinReservation(reservationData, type)}
          isDisabled={!checkinButtonState?.enabled}
          backgroundColor='brand.customBlue'
          color='white'
          width={{ base: 'full', md: 'auto' }}
          _hover={{
            backgroundColor: 'brand.customBlue'
          }}
        >
          {(() => {
            if(!checkinButtonState?.enabled) {
              return checkinButtonState?.text
            }
            return 'Check In'
          })()}
        </Button>
      </Box>
    </Flex>
  )
}


export default ReservationCard