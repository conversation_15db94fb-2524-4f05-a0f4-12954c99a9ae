import { Card, CardBody, CardHeader, Flex, <PERSON>ing, Stack, StackDivider, Text, useTheme } from "@chakra-ui/react"
import { TReservation, TUpcomingClass } from "../../../types/TReservation"
import ReservationCard from "./ReservationCard"
import { format } from "date-fns"
import { getReservationCheckinState } from "../utils/Reservation"
import { TbCalendarCancel } from "react-icons/tb"
import { convertMinutesToReadable } from "../../../utils/Date"
import useStartupData from "../../../hooks/useStartupData"

type TReservationsListProps = {
  fetchState: string,
  reservations: TReservation[],
  checkinReservation: (reservation: TReservation | TUpcomingClass, type: 'reservation' | 'upcoming') => void
}

function ReservationsList({
  fetchState,
  reservations,
  checkinReservation
}: TReservationsListProps) {

  const theme = useTheme()
  const { defaultClientConfig } = useStartupData();

  return (
    <Card
      boxShadow={'none'}
    >
      <CardHeader
        paddingX={0}
      >
        <Flex gap={4} alignItems='center'>
          <Heading fontWeight='800' size='lg'>Reservations</Heading>
        </Flex>
      </CardHeader>

      <CardBody
        paddingX={0}
      >
        <Stack divider={<StackDivider />} spacing='10'>
          {reservations.length === 0 &&
            <Flex
              alignItems='center'
              justifyContent='start'
              flexDirection='row'
              gap={4}
            >
              <TbCalendarCancel color={theme?.colors?.brand?.midGray} fontSize="30px" />
              <Text color='gray.700' mt={2} fontWeight='500' size='sm'>
                You do not have any reservations in the next {convertMinutesToReadable(defaultClientConfig?.configs?.class_checkin?.mins_before_class_to_show_reservation ?? 0)}.
              </Text>
            </Flex>
          }

          {reservations.map((reservation, index) => (
            <ReservationCard
              key={index}
              image={reservation?.images[0]}
              title={reservation.class_name || reservation.equipment_name || ''}
              instructor={+reservation.trainer_id ? `${reservation.instructor_first_name} ${reservation.instructor_last_name}` : null}
              startTime={format(new Date(reservation.start_time), 'h:mm aaa')}
              endTime={format(new Date(reservation.end_time), 'h:mm aaa')}
              room={reservation.room_name}
              checkinReservation={checkinReservation}
              reservationData={reservation}
              type="reservation"
              //  Get the checkin button state based on conditions
              checkinButtonState={getReservationCheckinState(reservation, fetchState)} />
          ))}
        </Stack>
      </CardBody>
    </Card>
  )
}

export default ReservationsList