import { Box, Center } from "@chakra-ui/react"
import ReservationsList from "./components/ReservationsList";
import SpinnerComponent from "../../components/SpinnerComponent";
import UpcomingClasses from "./components/UpcomingClasses";
import MainContainer from "../../components/MainContainer";
import SlideFadeAnimation from "../../components/Animations/SlideFadeAnimation";
import Header from "./components/Header";
import { useReservationHook } from "./hooks/useReservationHook";
import SalutationAndDate from "./components/SalutationAndDate";
import Divider from "../../components/Divider";
import HiddenTimeElement from "./components/HiddenTimeElement";


function Reservations() {

  const {
    user,
    fetchState,
    reservations,
    upcomingClasses,
    checkinReservation
  } = useReservationHook()
  
  if(fetchState === 'busy') {
    return (
      <Center h='500px'>
        <SpinnerComponent />
      </Center>
    )
  }

  return (
    <SlideFadeAnimation>
      <MainContainer>

        <Header />

        <SalutationAndDate user={user} />

        <Box mt={10}>
          <ReservationsList
            fetchState={fetchState}
            reservations={reservations}
            checkinReservation={checkinReservation} />
        </Box>

        <Box mt={10}>
          <Divider />
        </Box>

        <Box mt={10}>
          <UpcomingClasses
            fetchState={fetchState}
            upcoming={upcomingClasses}
           checkinReservation={checkinReservation} />
        </Box>
      </MainContainer>

      {fetchState !== 'busy' &&
        <HiddenTimeElement />
      }
    </SlideFadeAnimation>
  )
}

export default Reservations