import { useSearchParams } from "react-router-dom";
import useUpaceToast from "../../../hooks/useUpaceToast";
import { useEffect, useState } from "react";
import { TReservation, TUpcomingClass } from "../../../types/TReservation";
import useStartupData from "../../../hooks/useStartupData";
import { getMemberDataFromEncoding } from "../utils/Member";
import { checkin, fetchMemberReservations } from "../../../api/reservations";
import { queryServerStatus } from "../../../api/generic";
import { format } from "date-fns";


export const useReservationHook = () => {

  const [searchParams] = useSearchParams();
  const {
    showToast,
    closeAllToasts
  } = useUpaceToast()

  const [reservations, setReservations] = useState<TReservation[]>([])
  const [upcomingClasses, setUpcomingClasses] = useState<TUpcomingClass[]>([])
  const [fetchState, setFetchState] = useState('busy')

  const {
    uniId,
    defaultClientConfig,
    facility,
    navigate
  } = useStartupData()

  const encoded = searchParams.get('selected')
  const user = encoded ? getMemberDataFromEncoding(encoded) : null

  const getMemberReservations = async (setBusy = true) => {
    if(!user) {
      navigate(`/${uniId}`)
      return
    }
    
    if(setBusy) setFetchState('busy')

    const response = await fetchMemberReservations(user.memberId || '', uniId || '', facility?.id)
    if(response.status === 200) {
      setReservations(response.data.data)
      setUpcomingClasses(response.data.upcoming_classes)
    }
    setFetchState('fetched')
  }

  const checkinReservation = async (reservation: TReservation | TUpcomingClass, type: 'reservation' | 'upcoming') => {
    
    setFetchState(`saving:${reservation.id}`)

    const payload = type === 'reservation' ? {
      reservation_id: reservation.id,
      uni_id: uniId,
    } : {
      class_id: reservation.id,
      user_id: user?.memberId,
      date: format(new Date(), 'yyyy-MM-dd'),
      uni_id: uniId
    }

    showToast({
      title: `Attempting to checkin reservation...`,
      status: 'loading',
      duration: null
    })


    try {
      const response = await checkin(payload)
      closeAllToasts()

      showToast({
        title: response?.data?.message || `Checkin successful`,
        status: 'success',
        duration: 8000
      })

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      closeAllToasts()

      showToast({
        title: error?.response?.data?.message || `An error occured while checking in reservation`,
        status: 'error',
        duration: 8000
      })
      
      console.error("error", error)
    } finally {
      // 
      getMemberReservations(false)
      setFetchState('fetched')
    }

  }

  useEffect(() => {
    getMemberReservations()
    uniId && queryServerStatus(uniId)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  return {
    reservations,
    upcomingClasses,
    fetchState,
    checkinReservation,
    getMemberReservations,
    defaultClientConfig,
    facility,
    uniId,
    user
  }

}