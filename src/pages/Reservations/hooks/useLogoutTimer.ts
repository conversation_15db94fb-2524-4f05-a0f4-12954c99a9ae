import { useEffect, useRef } from "react";
import useStartupData from "../../../hooks/useStartupData";
import { useNavigate } from "react-router-dom";
import useUpaceToast from "../../../hooks/useUpaceToast";

export const useLogoutTimer = () => {
  const logoutSpyRef = useRef<NodeJS.Timeout>();
  const logoutNotificationRef = useRef<NodeJS.Timeout>();

  const { defaultClientConfig, uniId } = useStartupData();
  const { showToast, closeAllToasts } = useUpaceToast();

  const timeout =
    defaultClientConfig?.configs?.class_checkin?.logout_timeout_secs;

  const navigate = useNavigate();

  const logout = () => {
    navigate({
      pathname: `/${uniId}`,
    });
  };

  const resetTimer = () => {
    if (!timeout) return;

    if (logoutSpyRef.current) {
      clearTimeout(logoutSpyRef.current);
      clearTimeout(logoutNotificationRef.current);
    }
    closeAllToasts();

    logoutSpyRef.current = setTimeout(() => {
      logout();
    }, timeout * 1000);

    logoutNotificationRef.current = setTimeout(() => {
      showToast({
        title: "You will be logged out soon",
        status: "loading",
        duration: 3000,
      });
    }, timeout * 1000 - 3000);
  };

  useEffect(() => {
    const events = ["mousemove", "touchstart"];
    if (timeout) {
      // Add event listeners
      events.forEach((event) => window.addEventListener(event, resetTimer));

      resetTimer();
    }
    return () => {
      logoutSpyRef.current && clearTimeout(logoutSpyRef.current);
      logoutNotificationRef.current &&
        clearTimeout(logoutNotificationRef.current);
      events.forEach((event) => window.removeEventListener(event, resetTimer));
    };
  }, []);
};
