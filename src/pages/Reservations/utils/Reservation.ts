import { TReservation, TUpcomingClass } from "../../../types/TReservation";

export const getReservationCheckinState = (
  reservation: TReservation,
  fetchState: string,
): { enabled: boolean, text: string }  => {
  
  if (reservation.checkin == 1) {
    return { enabled: false, text: "Checked In" }
  }

  if(fetchState === `saving:${reservation.id}`) {
    return { enabled: false, text: "Checking In..." }
  }

  if(reservation.class_is_sgt === 1 && !reservation.membership_id) {
    return { enabled: false, text: "No Membership" }
  }

  if(reservation.equipment_type_id === 10 && !reservation.membership_id) {
    return { enabled: false, text: "No Membership" }
  }

  return { enabled: true, text: "Check In" }
}

export const getUpcomingClassesCheckinState = (
  upcoming: TUpcomingClass,
  fetchState: string,
): { enabled: boolean, text: string }  => {

  if(fetchState === `saving:${upcoming.id}`) {
    return { enabled: false, text: "Checking In..." }
  }

  if(upcoming.spots_available <= 0) {
    return { enabled: false, text: "Class Full" }
  }

  if(upcoming.cancelled) {
    return { enabled: false, text: "Cancelled" }
  }

  if(upcoming.facility_closed) {
    return { enabled: false, text: "Facility Closed" }
  }

  return { enabled: true, text: "Check In" }
}

export const getInstructorNameForUpcomingClasses = (upcoming: TUpcomingClass): string => {
  if(upcoming.is_class_subbed) {
    return upcoming.subbing_instructor || ""
  }

  return `${upcoming.instructor_first_name} ${upcoming.instructor_last_name}`;
}