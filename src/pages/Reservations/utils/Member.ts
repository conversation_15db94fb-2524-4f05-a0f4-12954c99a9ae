import { TReactSelectOption } from "../../../types/TSelect"
import { TUser } from "../../../types/TUser"

export const getMemberDataFromEncoding = (encoded: string): TUser => {
  const selectedMember = JSON.parse(atob(encoded)) as TReactSelectOption
  const { value: memberId, label: memberName } = selectedMember
  const user = {
    memberId,
    firstName: memberName?.split(' ')?.[0]?.trim(),
    lastName: memberName?.split(' ')?.[1].trim(),
    email: memberName?.split(' ')?.[2]?.trim(),
  }
  return user
}