import { Box, Flex, Input, Text } from "@chakra-ui/react"
import SlideFadeAnimation from "../../components/Animations/SlideFadeAnimation"
import MainContainer from "../../components/MainContainer"
import BreadCrumbs from "../../components/BreadCrumbs"
import LogoAndClientName from "../../components/LogoAndClientName"
import useStartupData from "../../hooks/useStartupData"
import { PiBarcodeLight } from "react-icons/pi"
import { useEffect, useRef, useState } from "react"
import { useDebounce } from "usehooks-ts"
import { searchMembersByBarcode } from "../../api/members"
import { createSearchParams } from "react-router-dom"
import useUpaceToast from "../../hooks/useUpaceToast"
import { queryServerStatus } from "../../api/generic"

function BarcodeInputScan() {

  const inputRef = useRef<HTMLInputElement>(null)
  const [inputValue, setInputValue] = useState('')
  const debounceInputValue = useDebounce(inputValue, 1500);
  const {
    showToast,
    closeAllToasts
  } = useUpaceToast()
  
  const {
    uniId,
    defaultClientConfig,
    facility,
    navigate
  } = useStartupData()

  const refocus = () => {
    if(inputRef?.current) {
      setTimeout(() => {
        inputRef?.current?.focus()
      }, 2000)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  const handleBarcodeUserSearch = async (barcode: string) => {
    
    showToast({
      title: `Searching user with barcode '${barcode}'...`,
      status: 'loading',
      duration: null,
    })

    try {
      const response = await searchMembersByBarcode(barcode, uniId || '')
      if(response.status === 200) {
        closeAllToasts()
        if(response?.data?.data.length > 0) {
          const member = response.data.data[0]
          const encoded = btoa(JSON.stringify({
            value: member?.id,
            label: `${member?.first_name} ${member?.last_name} (${member?.email})`
          }))
          navigate({
            pathname: `/reservations/${uniId}`,
            search: createSearchParams({
              selected: encoded
            }).toString()
          })
        } else {
          showToast({
            title: `User not found with barcode '${barcode}'`,
            status: 'error',
            duration: 5000
          })
        }
      }
    } catch(error) {
      closeAllToasts()
      showToast({
        title: `An error occurred while searching`,
        status: 'error',
        duration: 5000
      })
    } finally {
      setInputValue('')
      refocus()
    }
  }

  useEffect(() => {
    if(inputRef?.current) {
      inputRef?.current?.focus()
    }
    uniId && queryServerStatus(uniId)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  useEffect(() => {
    if(debounceInputValue) {
      handleBarcodeUserSearch(debounceInputValue)
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceInputValue])
  
  return (
    <SlideFadeAnimation>
      <MainContainer>

        <Box mb={5}>
          <BreadCrumbs paths={[
            {to: `/${uniId}`, text: 'Home', active: false}
          ]} />
        </Box>

        <LogoAndClientName
          clientName={defaultClientConfig?.client_name || ''}
          logo={defaultClientConfig?.configs?.class_checkin?.logo_url || ''}
          facilityName={facility?.name} />

        <Box mt={20}>
          <Flex gap={2} alignItems='center'>
            <PiBarcodeLight fontSize="35px" />
            <Text fontSize='xl'>Scan barcode</Text>
          </Flex>
          <Box mt={5}>
            <Input
              ref={inputRef}
              value={inputValue}
              onChange={handleInputChange}
              onBlur={refocus}
              placeholder='Waiting for barcode scan...' />
          </Box>
        </Box>
      </MainContainer>
    </SlideFadeAnimation>
  )
}

export default BarcodeInputScan