import { Box, Flex, Text } from "@chakra-ui/react"
import LogoAndClientName from "../../components/LogoAndClientName"
import { TbListSearch } from "react-icons/tb"
import SlideFadeAnimation from "../../components/Animations/SlideFadeAnimation"
import BreadCrumbs from "../../components/BreadCrumbs"
import MainContainer from "../../components/MainContainer"
import useStartupData from "../../hooks/useStartupData"
import MemberSearch from "../../components/MemberSearch"
import { useEffect } from "react"
import { queryServerStatus } from "../../api/generic"

function NameSearch() {

  const {
    uniId,
    defaultClientConfig,
    facility
  } = useStartupData()

  useEffect(() => {
    uniId && queryServerStatus(uniId)
  }, [])
  

  return (
    <SlideFadeAnimation>
      <MainContainer>

        <Box mb={5}>
          <BreadCrumbs paths={[
            {to: `/${uniId}`, text: 'Home', active: false},
            {to: `#`, text: 'Name Search', active: true},
          ]} />
        </Box>

        <LogoAndClientName
          clientName={defaultClientConfig?.client_name || ''}
          logo={defaultClientConfig?.configs?.class_checkin?.logo_url || ''}
          facilityName={facility?.name} />

        <Box mt={20}>
          <Flex gap={2} alignItems='center'>
            <TbListSearch fontSize="30px" />
            <Text fontSize='xl'>Search by member name</Text>
          </Flex>
          <Box mt={5}>
            {uniId &&
              <MemberSearch uniId={uniId} />
            }
          </Box>
        </Box>
      </MainContainer>
    </SlideFadeAnimation>
  )
}

export default NameSearch