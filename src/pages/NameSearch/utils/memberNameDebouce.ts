import debounce from "lodash.debounce";
import { TReactSelectOption } from "../../../types/TSelect";
import { searchMembersByName } from "../../../api/members";

export const debounceSearch = debounce(function (inputValue: string, uniId, callback: (options: TReactSelectOption[]) => void) {
  if(inputValue) {
      searchMembersByName(inputValue, uniId).then((response) => {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const options = response.data.data.map((user: any) => {
          return {
            label: `${user.first_name} ${user.last_name} (${user.email})`,
            value: user.id
          }
        })
        callback(options)
        // return options
      })
    } else {
      callback([])
    }  
}, 1000);