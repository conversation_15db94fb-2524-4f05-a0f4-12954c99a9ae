import { TFacility } from "../../../types/TFacility"
import Select, { SingleValue } from 'react-select'
import { convertToReactSelectOptions } from "../../../utils/Select"
import { Box, Text } from "@chakra-ui/react"
import { TReactSelectOption } from "../../../types/TSelect"
import { useParams } from "react-router-dom"
import { useDispatch } from "react-redux"
import { setSelectedFacility } from "../../../reduxtoolkit/slices/config"

type TFacilitySelectProps = {
  facilities: TFacility[]
  facility: TFacility | null
  universityId?: string
}

function FacilitySelect({
  facilities,
  facility,
  universityId
}: TFacilitySelectProps) {

  const dispatch = useDispatch()
  const { uniId } = useParams()

  const handleFacilityChange = (value: SingleValue<TReactSelectOption> | null) => {
    dispatch(setSelectedFacility({ uniId: (uniId || universityId), selectedFacility: {
      id: parseInt(value?.value || ''),
      name: value?.label || ''
    } }))

    window.location.reload()
  }

  return (
    <Box>
      <Text fontWeight='800' mb={1}>Facility</Text>
      <Select
        onChange={(value: SingleValue<TReactSelectOption> | null) => handleFacilityChange(value)}
        value={{
          value: facility?.id?.toString(),
          label: facility?.name
        }}
        placeholder='All Facilities '
        options={convertToReactSelectOptions(facilities, 'id', 'name')} />
    </Box>
  )
}

export default FacilitySelect