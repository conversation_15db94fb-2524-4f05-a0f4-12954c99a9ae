import { Button, ButtonProps } from "@chakra-ui/react"
import { IoMdBarcode } from "react-icons/io"
import { TConfig } from "../../../types/TConfig"
import { HiOutlineArrowRight } from 'react-icons/hi'
import { useNavigate, useParams } from "react-router-dom"

type TActionButtonProps = {
  clientConfig: TConfig
}

function ActionButtons({
  clientConfig
}: TActionButtonProps) {

  const { uniId } = useParams()
  const navigate = useNavigate()

  return (
    <>
      {clientConfig?.configs?.class_checkin?.checkin_via_barcode_image_scan &&
        
          <Button
            rightIcon={<HiOutlineArrowRight />}
            leftIcon={<IoMdBarcode />}
          >
            Barcode Image Scan
          </Button>
      }
      
      {clientConfig?.configs?.class_checkin?.checkin_via_barcode_text_input &&
        <ActionBtn
          onClick={() => navigate(`/barcodeinput/${uniId}`) }
          rightIcon={<HiOutlineArrowRight />}
          leftIcon={<IoMdBarcode />}
        >
          Barcode Input Scan
        </ActionBtn>
      }

      {/* {clientConfig?.configs?.class_checkin?.checkin_via_name_search &&
        <ActionBtn
          onClick={() => navigate(`/namesearch/${uniId}`) }
          rightIcon={<HiOutlineArrowRight />}
          leftIcon={<FaRegUser />}
        >
          Name Search
        </ActionBtn>
      } */}
    </>
  )
}

const ActionBtn = ({
  children,
  rightIcon,
  leftIcon,
  ...rest
}: ButtonProps & {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  rightIcon?: React.ReactElement<any, string | React.JSXElementConstructor<any>> | undefined
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  leftIcon?: React.ReactElement<any, string | React.JSXElementConstructor<any>> | undefined
}) => {
  return (
    <Button
      justifyContent={'start'}
      maxW='500px'
      rightIcon={rightIcon}
      leftIcon={leftIcon}
      colorScheme='teal'
      variant='solid'
      {...rest}
    >
      {children}
    </Button>
  )
}

export default ActionButtons