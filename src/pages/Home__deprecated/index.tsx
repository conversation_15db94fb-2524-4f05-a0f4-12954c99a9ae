import { Box, Center, Flex, Text } from "@chakra-ui/react"
import { fetchUniversityConfig } from "../../api/uni"
import { useEffect } from "react"
import { useDispatch } from "react-redux"
import { setClientConfig } from "../../reduxtoolkit/slices/config"
import SpinnerComponent from "../../components/SpinnerComponent"
import LogoAndClientName from "../../components/LogoAndClientName"
import SlideFadeAnimation from "../../components/Animations/SlideFadeAnimation"
import ActionButtons from "./components/ActionButtons"
import MainContainer from "../../components/MainContainer"
import useStartupData from "../../hooks/useStartupData"
import MemberSearch from "../../components/MemberSearch"
import { useNavigate } from "react-router-dom"
import { queryServerStatus } from "../../api/generic"

function Home() {

  const dispatch = useDispatch()
  const navigate = useNavigate()

  const {
    uniId,
    defaultClientConfig,
    facility
  } = useStartupData()

  const fetchUniConfig = async () => {
    if(uniId) {
      try {
        const response = await fetchUniversityConfig(uniId)
        if(response.status === 200) {
          if(!response.data.data.configs) {
            navigate({
              pathname: `/invalid`,
            })
          }
          dispatch(setClientConfig({ uniId, clientConfig: response.data.data }))
        }
      } catch(e) {
        navigate({
          pathname: `/invalid`,
        })
      }
    }
  }

  useEffect(() => {
    fetchUniConfig()
    uniId && queryServerStatus(uniId)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [])

  if(!defaultClientConfig) {
    return (
      <Center h='500px'>
        <SpinnerComponent />
      </Center>
    )
  }

  return (
    <SlideFadeAnimation>
      <MainContainer>
        <Flex width="100%" justifyContent='space-between' gap={10}>
          <Box
              min-height='80vh'
              width="full"
              minHeight='80vh'
            >
            <LogoAndClientName
              logo={defaultClientConfig?.configs?.class_checkin?.logo_url}
              clientName={defaultClientConfig.client_name}
              facilityName={facility?.name} />

            {/* <Flex alignItems='center' gap={2} mt={2}>
              <PiStampThin fontSize="20px" />
              <Text fontSize='xl'>Checkin Tool</Text>
            </Flex> */}

            <Box mt={10}>
              {uniId &&
                <Box maxW='500px'>
                  <Text fontWeight='600' mb={2}>Search by Member's Name</Text>
                  <MemberSearch uniId={uniId} />
                  <Center mt={5}>OR</Center>
                </Box>
              }
              {/* <FacilitySelect
                facilities={defaultClientConfig?.facilities}
                facility={facility} /> */}
            </Box>

            <Flex flexDirection='column' gap={5} mt={5}>
              <ActionButtons clientConfig={defaultClientConfig} />
            </Flex>
          </Box>

          {/* SIDE IMAGE */}
          <Box
            display={['none', 'none', 'block', 'block']}
            backgroundImage={defaultClientConfig?.configs?.class_checkin?.background_image_url}
            backgroundRepeat='no-repeat'
            backgroundSize='cover'
            backgroundPosition='center'
            width="full"
            minHeight='80vh'
          >
          </Box>
        </Flex>
      </MainContainer>
    </SlideFadeAnimation>
  )
}

export default Home