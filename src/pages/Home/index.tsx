import { useEffect } from "react";
import { fetchUniversityConfig } from "../../api/uni";
import useStartupData from "../../hooks/useStartupData";
import { useNavigate } from "react-router-dom";
import { setClientConfig } from "../../reduxtoolkit/slices/config";
import { queryServerStatus } from "../../api/generic";
import { Center } from "@chakra-ui/react";
import SpinnerComponent from "../../components/SpinnerComponent";
import MainContent from "./MainContent";
import { useDispatch } from "react-redux";

function Home() {
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { uniId, defaultClientConfig } = useStartupData();

  const fetchUniConfig = async () => {
    if (uniId) {
      try {
        const response = await fetchUniversityConfig(uniId);
        if (response.status === 200) {
          if (!response.data.data.configs) {
            navigate({
              pathname: `/invalid`,
            });
          }
          dispatch(
            setClientConfig({ uniId, clientConfig: response.data.data })
          );
        }
      } catch (e) {
        navigate({
          pathname: `/invalid`,
        });
      }
    }
  };

  useEffect(() => {
    fetchUniConfig();
    uniId && queryServerStatus(uniId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!defaultClientConfig) {
    return (
      <Center h="500px">
        <SpinnerComponent />
      </Center>
    );
  }

  return <MainContent />;
}

export default Home;
