import { Bad<PERSON>, Box, Flex, Text } from "@chakra-ui/react";
import { useState } from "react";
import SlideFadeAnimation from "../../components/Animations/SlideFadeAnimation";
import useStartupData from "../../hooks/useStartupData";
import HomeContainer from "./components/HomeContainer";
import OrButtonIndicator from "./components/OrButtonIndicator";
import { getAvailableMemberSelections } from "./utils/CheckinSearch";
import { memberInputSelectionMap } from "./utils/Constants";
import LogoContainer from "./components/LogoContainer";
import { FaLocationPin } from "react-icons/fa6";
import FacilitySelectorButton from "../../components/FacilitySelectorButton";

function MainContent() {
  const { defaultClientConfig, facility } = useStartupData();

  const [memberSearchMethods, setMemberSearchMethods] = useState<string[]>(
    getAvailableMemberSelections(defaultClientConfig)
  );

  const handleSearchSelectionChange = (selector: string) => {
    const __memberSearchMethods = [...memberSearchMethods];

    //  Remove selected item and shit to beginning of array
    const index = __memberSearchMethods.indexOf(selector);
    if (index !== -1) {
      __memberSearchMethods.splice(index, 1);
    }
    __memberSearchMethods.unshift(selector);
    setMemberSearchMethods(__memberSearchMethods);
  };

  return (
    <SlideFadeAnimation>
      <HomeContainer
        backgroundImage={
          defaultClientConfig?.configs?.class_checkin?.background_image_url
        }
      >
        <Flex
          flexDirection={{
            base: "column-reverse",
            md: "row",
          }}
          minW={{
            base: "98%",
            md: "568px",
          }}
          position={"relative"}
          borderRadius={{
            base: "unset",
            md: "xl",
          }}
          background={"transparent"}
        >
          <Box
            padding={5}
            flexGrow={2}
            borderTopLeftRadius={["unset", "unset", "xl"]}
            borderBottomLeftRadius={"xl"}
            borderBottomRightRadius={["xl", "xl", "unset"]}
            backgroundColor={"#FFF"}
          >
            <Box textAlign={"center"}>
              <Text
                color="brand.customGreen"
                fontSize={"4xl"}
                fontWeight={"900"}
              >
                WELCOME
              </Text>
              <Text
                color="brand.customGreen"
                fontSize={"xl"}
                fontWeight={"700"}
              >
                CHECK-IN
              </Text>
              <Badge px={2}>
                <Flex alignItems="center" gap={2}>
                  <FaLocationPin fontSize="12px" aria-hidden="true" />{" "}
                  <Text fontSize="xs">
                    {facility?.name || "All Facilities"}
                  </Text>
                </Flex>
              </Badge>
            </Box>

            <Box mt={5}>
              {memberInputSelectionMap[memberSearchMethods[0]]?.component}
            </Box>

            {memberSearchMethods.length > 1 && (
              <Box mt={5}>
                <OrButtonIndicator />
              </Box>
            )}

            <Box mt={5}>
              {memberSearchMethods.map((method, index) => {
                if (index === 0) return null;
                const selectedComponent = memberInputSelectionMap[method];

                if (typeof selectedComponent.selector === "function")
                  return selectedComponent.selector(index);

                return (
                  <Box
                    onClick={() => handleSearchSelectionChange(method)}
                    cursor={"pointer"}
                    key={index}
                    role="button"
                    tabIndex={0}
                    onKeyDown={(e) => {
                      if (e.key === "Enter" || e.key === " ") {
                        handleSearchSelectionChange(method);
                      }
                    }}
                    aria-label={`Switch to ${method
                      .replace("checkin_via_", "")
                      .replace(/_/g, " ")} method`}
                  >
                    {selectedComponent.selector}
                  </Box>
                );
              })}
            </Box>
          </Box>
          <LogoContainer
            companyName={defaultClientConfig?.client_name}
            logo={defaultClientConfig?.configs?.class_checkin?.logo_url}
          />
        </Flex>
      </HomeContainer>

      <FacilitySelectorButton />
    </SlideFadeAnimation>
  );
}

export default MainContent;
