import {
  BarcodeInputScan,
  BarcodeInputSelector,
} from "../components/BarcodeInputScan";
import { CameraBarcodeScan } from "../components/CameraBarcodeScan";
import { NameSearch, NameSearchSelector } from "../components/NameSearch";

export const memberInputSelections = {
  NAME_SEARCH: "checkin_via_name_search",
  BARCODE_INPUT_SCAN: "checkin_via_barcode_text_input",
  BARCODE_IMAGE_SCAN: "checkin_via_barcode_image_scan",
};

export const memberInputSelectionMap = {
  [memberInputSelections.NAME_SEARCH]: {
    selector: <NameSearchSelector />,
    component: <NameSearch />,
  },
  [memberInputSelections.BARCODE_INPUT_SCAN]: {
    selector: <BarcodeInputSelector />,
    component: <BarcodeInputScan />,
  },
  [memberInputSelections.BARCODE_IMAGE_SCAN]: {
    selector: (index?: number) => <CameraBarcodeScan optionIndex={index} />,
    component: <CameraBarcodeScan />,
  },
};
