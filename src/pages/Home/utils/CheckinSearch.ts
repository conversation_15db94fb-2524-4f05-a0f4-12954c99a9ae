import { TConfig } from "../../../types/TConfig"
import { memberInputSelections } from "./Constants"

export const getAvailableMemberSelections = (config: TConfig | null) => {

  if(config) {

    if(config.configs?.class_checkin?.checkin_user_search_options) {
      return config.configs?.class_checkin?.checkin_user_search_options
    }

    //  TODO -> remove this when clients local storage is updated
    return [
      ...(config?.configs?.class_checkin?.checkin_via_name_search ? [memberInputSelections.NAME_SEARCH] : []),
      ...(config?.configs?.class_checkin?.checkin_via_barcode_text_input ? [memberInputSelections.BARCODE_INPUT_SCAN] : []),
      ...(config?.configs?.class_checkin?.checkin_via_barcode_image_scan ? [memberInputSelections.BARCODE_IMAGE_SCAN] : []),
    ]
  }

  return [
  ]
}