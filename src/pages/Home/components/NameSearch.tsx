import { Box, Flex, Text, Button } from "@chakra-ui/react";
import MemberSearch from "../../../components/MemberSearch";
import useStartupData from "../../../hooks/useStartupData";
import { IoIosSearch } from "react-icons/io";

export const NameSearch = () => {
  const { uniId } = useStartupData();

  return (
    <Box>
      <Flex gap={2} justifyContent={"center"} alignItems="center">
        <IoIosSearch fontSize="30px" aria-hidden="true" />
        <Text className="sr-only">Search by name</Text>
      </Flex>
      <Box mt={2}>{uniId && <MemberSearch uniId={uniId} />}</Box>
    </Box>
  );
};

export const NameSearchSelector = () => {
  return (
    <Button
      variant="unstyled"
      display="flex"
      flexDirection="column"
      justifyContent="center"
      alignItems="center"
      aria-label="Select name search method"
      height="auto"
      p={2}
    >
      <IoIosSearch fontSize="50px" aria-hidden="true" />
      <Text fontSize="lg">Search by first / last name</Text>
    </Button>
  );
};
