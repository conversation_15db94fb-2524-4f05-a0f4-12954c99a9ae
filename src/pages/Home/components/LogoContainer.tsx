import { Flex, Image } from "@chakra-ui/react";

type TLogoContainerProps = {
  logo?: string;
  companyName?: string;
};

function LogoContainer({ logo, companyName }: TLogoContainerProps) {
  return (
    <Flex
      flexGrow={1}
      opacity={0.9}
      borderTopLeftRadius={["xl", "xl", "unset"]}
      borderTopRightRadius={"xl"}
      borderBottomRightRadius={["unset", "unset", "xl"]}
      paddingY={{
        base: 4,
        md: 0,
      }}
      justifyContent={"center"}
      alignItems={"center"}
      backgroundColor={"brand.customGreen"}
    >
      <Image fit={"contain"} w="100px" src={logo} alt={`${companyName} logo`} />
    </Flex>
  );
}

export default LogoContainer;
