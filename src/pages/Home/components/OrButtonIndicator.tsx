import { Button, Flex } from '@chakra-ui/react'

function OrButtonIndicator() {
  return (
    <Flex justifyContent={'center'}>
      <Button
        mt={3}
        cursor={'default'}
        backgroundColor={'brand.customDark'}
        color={'#FFF'}
        borderRadius={'50%'}
        fontWeight={'bold'}
        fontSize={'xs'}
        _hover={{
          backgroundColor: 'brand.customDark'
        }}
        width={'40px'}
        height={'40px'}>
        OR
      </Button>
    </Flex>
  )
}

export default OrButtonIndicator