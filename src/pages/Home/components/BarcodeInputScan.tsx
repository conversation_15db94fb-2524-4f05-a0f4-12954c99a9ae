import { Box, Flex, Input, Text } from "@chakra-ui/react";
import { Fragment, useEffect, useRef, useState } from "react";
import { useDebounce } from "usehooks-ts";
import useUpaceToast from "../../../hooks/useUpaceToast";
import useStartupData from "../../../hooks/useStartupData";
import { queryServerStatus } from "../../../api/generic";
import { MdBarcodeReader } from "react-icons/md";
import { useBarcodeScan } from "../../../hooks/useBarcodeScan";

export const BarcodeInputScan = () => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [inputValue, setInputValue] = useState("");
  const debounceInputValue = useDebounce(inputValue, 1000);
  const { showToast, closeAllToasts } = useUpaceToast();

  const { uniId } = useStartupData();

  /**
   * This function exists to check if the input is the topmost element on the page and control the refocus function
   * Refocus was being called even when a modal is open and hence disturbing the select input field
   */
  const isInputTopmostElement = () => {
    const inputElement = document.getElementById("barcode-input-scan");

    if (!inputElement) {
      return true;
    }

    const rect = inputElement.getBoundingClientRect();

    const centerX = rect.left + rect.width / 2;
    const centerY = rect.top + rect.height / 2;

    const topElement = document.elementFromPoint(centerX, centerY);

    return inputElement === topElement;
  };

  const refocus = () => {
    if (inputRef?.current) {
      setTimeout(() => {
        if (!isInputTopmostElement()) return;
        inputRef?.current?.focus();
      }, 2000);
    }
  };

  const handleBarcodeUserSearch = useBarcodeScan({
    onError: () => {
      closeAllToasts();
      showToast({
        title: `An error occurred while searching`,
        status: "error",
        duration: 5000,
      });
    },
    onSuccess: () => {
      setInputValue("");
      refocus();
    },
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  useEffect(() => {
    if (inputRef?.current) {
      inputRef?.current?.focus();
    }
    uniId && queryServerStatus(uniId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (debounceInputValue) {
      handleBarcodeUserSearch(debounceInputValue);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [debounceInputValue]);

  return (
    <Fragment>
      <Box>
        <Flex gap={2} justifyContent={"center"} alignItems="center">
          <MdBarcodeReader fontSize="30px" aria-hidden="true" />
          <Text className="sr-only">Barcode scanner input</Text>
        </Flex>
        <Box mt={2}>
          <Input
            ref={inputRef}
            id="barcode-input-scan"
            value={inputValue}
            onChange={handleInputChange}
            onBlur={refocus}
            placeholder="Waiting for barcode scan..."
            aria-label="Barcode input field"
            aria-describedby="barcode-input-instructions"
          />
          <div id="barcode-input-instructions" className="sr-only">
            This field automatically captures barcode scans. You can also
            manually type a barcode number and it will be processed after a
            short delay.
          </div>
        </Box>
      </Box>
    </Fragment>
  );
};

export const BarcodeInputSelector = () => {
  return (
    <Flex
      flexDirection={"column"}
      justifyContent={"center"}
      alignItems={"center"}
      role="button"
      tabIndex={0}
      aria-label="Select barcode input scanning method"
    >
      <MdBarcodeReader fontSize="70px" aria-hidden="true" />
      <Text fontSize="lg">Scan Your Barcode</Text>
    </Flex>
  );
};
