import {
  Center,
  Button,
  Flex,
  Modal,
  ModalBody,
  ModalCloseButton,
  Modal<PERSON>ontent,
  ModalHeader,
  ModalOverlay,
  Text,
  Box,
  useDisclosure,
} from "@chakra-ui/react";
import { useCallback, useEffect, useRef, useState } from "react";
import { useBarcodeScan } from "../../../hooks/useBarcodeScan";
import { MdOutlineCameraswitch } from "react-icons/md";
import { FaBarcode } from "react-icons/fa";

import { BarcodeScanner } from "./BarcodeCustom/BarcodeCustom";
import useStartupData from "../../../hooks/useStartupData";

export const CameraBarcodeScan = ({
  optionIndex,
}: {
  optionIndex?: number;
}) => {
  const { defaultClientConfig } = useStartupData();

  const [cameraEnv, setCameraEnv] = useState("");

  const handleBarcodeUserSearch = useBarcodeScan();

  const { isO<PERSON>, on<PERSON><PERSON>, onClose } = useDisclosure();

  const handleSearch = useCallback(
    (result?: string) => {
      if (result) {
        return handleBarcodeUserSearch(result);
      }
      return;
    },
    [handleBarcodeUserSearch]
  );

  const handleSwitchCamera = useCallback(
    (view?: string) =>
      setCameraEnv(() => {
        if (view) {
          return view;
        }
        return cameraEnv === "back" ? "front" : "back";
      }),
    [cameraEnv, setCameraEnv]
  );

  const targetRef = useRef(null);

  const mapCameraEnvToOriginal = (env: string) =>
    env === "back" ? "environment" : "user";

  const handleOpen = () => {
    onOpen();
    setTimeout(() => handleSwitchCamera(cameraEnv), 2);
  };

  // Todo: Refactor, temporal solution
  useEffect(() => {
    if (defaultClientConfig?.configs?.class_checkin?.camera_position) {
      setCameraEnv(
        defaultClientConfig?.configs?.class_checkin?.camera_position
      );
    }
  }, [defaultClientConfig?.configs?.class_checkin?.camera_position]);

  return (
    <Flex
      flexDirection={"column"}
      justifyContent={"center"}
      alignItems={"center"}
    >
      <Box
        cursor={"pointer"}
        onClick={handleOpen}
        role="button"
        aria-label="Open barcode scanner"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === "Enter" || e.key === " ") {
            handleOpen();
          }
        }}
      >
        <Flex
          flexDirection={"column"}
          justifyContent={"center"}
          alignItems={"center"}
        >
          {/* TODO: Hack to render different text based on the position of check in options */}
          <FaBarcode
            fontSize={!optionIndex ? "30px" : "70px"}
            aria-hidden="true"
          />
          {!optionIndex ? (
            <Button
              style={{ marginTop: 10 }}
              colorScheme="gray"
              aria-label="Click to scan barcode image"
            >
              Click to scan image
            </Button>
          ) : (
            <Text fontSize="lg">Scan Your Barcode</Text>
          )}
        </Flex>
      </Box>

      <Modal
        key={optionIndex}
        onClose={onClose}
        size="3xl"
        isOpen={isOpen}
        isCentered
        aria-labelledby="barcode-scanner-modal-title"
        returnFocusOnClose={true}
      >
        <ModalOverlay
          bg="blackAlpha.300"
          backdropFilter="blur(10px) hue-rotate(90deg)"
        />
        <ModalContent>
          <ModalHeader id="barcode-scanner-modal-title">
            Place your barcode in front of the camera
          </ModalHeader>
          <ModalCloseButton aria-label="Close barcode scanner" />
          <ModalBody>
            <div
              ref={targetRef}
              id="interactive"
              className="viewport"
              role="region"
              aria-label="Barcode scanner camera view"
            >
              <BarcodeScanner
                key={optionIndex}
                targetRef={targetRef}
                facingMode={mapCameraEnvToOriginal(cameraEnv)}
                onDetected={handleSearch}
              />
              <div aria-live="polite" className="sr-only">
                Camera is active. When a barcode is detected, it will be
                automatically scanned.
              </div>
            </div>
          </ModalBody>
          <Center>
            <Button
              id="switch-camera-btn"
              style={{ margin: "10px" }}
              size="sm"
              variant="outline"
              colorScheme="gray"
              onClick={() => handleSwitchCamera()}
              rightIcon={<MdOutlineCameraswitch aria-hidden="true" />}
              aria-label="Switch between front and back camera"
            >
              Switch camera
            </Button>
          </Center>
        </ModalContent>
      </Modal>
    </Flex>
  );
};
