/* eslint-disable @typescript-eslint/ban-ts-comment */
// @ts-nocheck
import { FC, useCallback, useEffect } from "react";
import Quagga from "@ericblade/quagga2";

import "./style.css";

interface BarcodeScannerProps {
  onDetected: (code: string) => void;
  facingMode: string;
  targetRef: React.MutableRefObject<HTMLVideoElement | null>;
}

interface Result {
  codeResult: {
    code: string;
  };
}

export const BarcodeScanner: FC<BarcodeScannerProps> = ({
  facingMode = "environment",
  onDetected,
  targetRef,
}) => {
  const handleDetected = useCallback(
    (result: Result) => {
      if (result?.codeResult?.code) {
        // Create an announcement for screen readers when a barcode is detected
        const announcement = document.createElement("div");
        announcement.setAttribute("aria-live", "assertive");
        announcement.setAttribute("role", "status");
        announcement.classList.add("sr-only");
        announcement.textContent = `Barcode detected: ${result.codeResult.code}. Processing...`;
        document.body.appendChild(announcement);

        // Remove the announcement after it's been read
        setTimeout(() => {
          document.body.removeChild(announcement);
        }, 3000);

        return onDetected(result.codeResult.code);
      }
      return;
    },
    [onDetected]
  );

  useEffect(() => {
    // Announce camera status to screen readers
    const cameraStatusAnnouncement = document.createElement("div");
    cameraStatusAnnouncement.setAttribute("aria-live", "polite");
    cameraStatusAnnouncement.setAttribute("role", "status");
    cameraStatusAnnouncement.classList.add("sr-only");
    cameraStatusAnnouncement.textContent = `Camera is ${
      facingMode === "environment" ? "back" : "front"
    } facing. Ready to scan barcodes.`;
    document.body.appendChild(cameraStatusAnnouncement);

    // Remove the announcement after it's been read
    setTimeout(() => {
      document.body.removeChild(cameraStatusAnnouncement);
    }, 3000);

    if (
      navigator.mediaDevices &&
      typeof navigator.mediaDevices.getUserMedia === "function" &&
      targetRef.current
    ) {
      Quagga.init(
        {
          inputStream: {
            type: "LiveStream",
            constraints: {
              width: { min: 400 },
              // height: { min: 300 },
              facingMode,
            },
            target: targetRef.current,
          },
          numOfWorkers: 2,
          frequency: 2,
          decoder: { readers: ["code_128_reader", "code_39_reader"] },
          locate: true,
        },
        (err: unknown) => {
          if (err) {
            console.log(err, "error msg");

            // Announce error to screen readers
            const errorAnnouncement = document.createElement("div");
            errorAnnouncement.setAttribute("aria-live", "assertive");
            errorAnnouncement.setAttribute("role", "alert");
            errorAnnouncement.classList.add("sr-only");
            errorAnnouncement.textContent =
              "Error initializing camera. Please try again or use a different check-in method.";
            document.body.appendChild(errorAnnouncement);

            // Remove the announcement after it's been read
            setTimeout(() => {
              document.body.removeChild(errorAnnouncement);
            }, 5000);
          }
          Quagga.start();
          return () => {
            Quagga.stop();
            Quagga.offDetected(handleDetected);
          };
        }
      );

      Quagga.onDetected(handleDetected);
      return () => {
        Quagga.stop();
        Quagga.offDetected(handleDetected);
      };
    }
  }, [facingMode, handleDetected, targetRef]);

  return null;
};
