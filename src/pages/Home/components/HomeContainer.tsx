import { Flex } from '@chakra-ui/react'
import { ReactNode } from 'react'

type THomeContainerProps = {
  backgroundImage?: string,
  children: ReactNode
}

function HomeContainer({
  backgroundImage,
  children
}: THomeContainerProps) {
  return (
    <Flex
      alignItems={'center'}
      justifyContent={'center'}
      minHeight={'100dvh'}
      _before={{
        content: '""',
        display: 'block',
        position: 'absolute',
        background: 'rgba(0, 0, 0, 0.5);',
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        width: '100%',
      }}
      backgroundImage={`url(${backgroundImage})`}
      backgroundRepeat={'no-repeat'}
      backgroundSize={'cover'}
      backgroundPosition={'center'}>
        {children}
    </Flex>
  )
}

export default HomeContainer