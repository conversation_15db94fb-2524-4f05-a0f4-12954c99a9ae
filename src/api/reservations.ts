import axiosInstance from "../http/Axios"

export const fetchMemberReservations = async (
  memberId: string,
  uniId: string,
  gymId: number | undefined = undefined
) => {
  return await axiosInstance.get(`/checkintool/reservations`,{
    params:  {
      user_id: memberId,
      uni_id: uniId,
      gym_id: gymId
    }
  })
}

export const checkin = async (payload: object) => {
  return await axiosInstance.post(`/checkintool/reservations/checkin`, payload)
}