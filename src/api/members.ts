import axiosInstance from "../http/Axios"

export const searchMembersByName = async (search: string, uniId: number | string) => {
  return await axiosInstance.post(`/checkintool/search`, {
    name_search: search,
    uni_id: uniId
  })
}

export const searchMembersByBarcode = async (barcode: string, uniId: number | string) => {
  return await axiosInstance.post(`/checkintool/search`, {
    barcode_search: barcode,
    uni_id: uniId
  })
}