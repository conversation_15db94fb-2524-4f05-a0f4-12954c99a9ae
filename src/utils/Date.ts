export const convertTimeFormatToDate = (time: string) => {
  // Split the time string into its components
  const [hours, minutes, seconds] = time.split(':');

  // Create a new Date object for the current date
  const date = new Date();

  // Set hours, minutes, and seconds from the time string
  date.setHours(parseInt(hours));
  date.setMinutes(parseInt(minutes));
  date.setSeconds(parseInt(seconds));

  return date;
}

export const convertMinutesToReadable = (minutes: number) => {
  const hours = Math.floor(minutes / 60)
  const remainingMinutes = Math.round(minutes % 60)

  if(hours === 0) return `${remainingMinutes} minute${remainingMinutes > 1 ? 's' : ''}`;
  if(remainingMinutes === 0) return `${hours} hour${hours > 1 ? 's' : ''}`;

  return `${hours} hour${hours > 1 ? 's' : ''} ${remainingMinutes} minute${remainingMinutes > 1 ? 's' : ''}`;
}