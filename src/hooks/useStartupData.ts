import { useSelector } from 'react-redux'
import { RootState } from '../reduxtoolkit/store'
import { useNavigate, useParams } from 'react-router-dom'

function useStartupData() {
  const { uniId } = useParams()
  const navigate = useNavigate()
  const { clientConfig, selectedFacility } = useSelector((state: RootState) => state.configSlice)

  const defaultClientConfig = uniId ? clientConfig?.[uniId] : null
  const facility = uniId ? selectedFacility?.[uniId] : null

  return {
    uniId,
    navigate,
    defaultClientConfig,
    facility
  }
}

export default useStartupData