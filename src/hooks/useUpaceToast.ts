import { useToast } from "@chakra-ui/react"

function useUpaceToast() {
  
  const toast = useToast()

  const showToast = ({
    title,
    status,
    duration
  }: {
    title: string,
    status: "success" | "error" | "warning" | "info" | "loading",
    duration: number | null
  
  }) => {
    toast({
      title: title,
      status: status,
      duration: duration,
      isClosable: true,
      position: 'top'
    })
  }

  const closeAllToasts = () => {
    toast.closeAll()
  }

  return {
    showToast,
    closeAllToasts
  }
}

export default useUpaceToast