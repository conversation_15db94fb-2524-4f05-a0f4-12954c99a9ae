import useStartupData from "./useStartupData";
import useUpaceToast from "./useUpaceToast";
import { searchMembersByBarcode } from "../api/members";
import { createSearchParams } from "react-router-dom";

type BarcodeScanOptionsType = {
  onError?: () => void;
  onSuccess?: () => void;
};

export const useBarcodeScan = (options?: BarcodeScanOptionsType) => {
  const { showToast, closeAllToasts } = useUpaceToast();

  const { uniId, navigate } = useStartupData();

  return async (barcode: string) => {
    showToast({
      title: `Searching user with barcode '${barcode}'...`,
      status: "loading",
      duration: null,
    });

    try {
      const response = await searchMembersByBarcode(barcode, uniId || "");
      if (response.status === 200) {
        closeAllToasts();
        if (response?.data?.data.length > 0) {
          const member = response.data.data[0];
          const encoded = btoa(
            JSON.stringify({
              value: member?.id,
              label: `${member?.first_name} ${member?.last_name} (${member?.email})`,
            })
          );
          navigate({
            pathname: `/reservations/${uniId}`,
            search: createSearchParams({
              selected: encoded,
            }).toString(),
          });
        } else {
          showToast({
            title: `User not found with barcode '${barcode}'`,
            status: "error",
            duration: 5000,
          });
        }
      }
    } catch (error) {
      options?.onError?.();
    } finally {
      options?.onSuccess?.();
    }
  };
};
