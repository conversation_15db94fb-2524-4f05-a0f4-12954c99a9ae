import { useCallback } from 'react'
import { debounceSearch } from '../pages/NameSearch/utils/memberNameDebouce'
import { TReactSelectOption } from '../types/TSelect'
import { SingleValue } from 'react-select'
import { createSearchParams, useNavigate } from 'react-router-dom'

function useMemberSearch(uniId: string) {

  const navigate = useNavigate()

  const searchMembers = useCallback((inputValue: string, callback: (options: TReactSelectOption[]) => void) => {
    debounceSearch(inputValue, uniId, callback)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  },[])
  
  const onMemberSelect = (value: SingleValue<TReactSelectOption>) => {
    const encoded = btoa(JSON.stringify(value))
    navigate({
      pathname: `/reservations/${uniId}`,
      search: createSearchParams({
        selected: encoded
      }).toString()
    })
  }

  return {
    searchMembers,
    onMemberSelect
  }
}

export default useMemberSearch