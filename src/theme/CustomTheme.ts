import { extendTheme } from "@chakra-ui/react";

const customTheme = extendTheme({
  fonts: {
    heading: `'Montserrat', sans-serif`,
    body: `'Montserrat', sans-serif`,
  },
  breakpoints: {
    sm: "480px",
    md: "768px",
    lg: "992px",
    xl: "1280px",
    "2xl": "1536px",
  },
  styles: {
    global: {
      "html, body": {
        backgroundColor: "#FFF",
      },
      ".sr-only": {
        position: "absolute",
        width: "1px",
        height: "1px",
        padding: "0",
        margin: "-1px",
        overflow: "hidden",
        clip: "rect(0, 0, 0, 0)",
        whiteSpace: "nowrap",
        borderWidth: "0",
      },
    },
  },
  colors: {
    brand: {
      midGray: "#A1A1A1",
      lightBlue: "#00acc8",
      customGreen: "#00A1C5",
      customDark: "#434243",
      customBlue: "#009DC4",
    },
  },
});

export default customTheme;
