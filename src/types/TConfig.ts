import { TFacility } from "./TFacility";

export type TConfig = {
  client_name: string;
  configs: {
    class_checkin: {
      checkin_user_search_options: string[];
      background_image_url: string;
      checkin_via_barcode_image_scan: boolean;
      checkin_via_barcode_text_input: boolean;
      checkin_via_name_search: boolean;
      logo_url: string;
      mins_after_class_to_show_reservation: number;
      mins_before_class_to_show_reservation: number;
      logout_timeout_secs: number;
      camera_position?: string;
    };
  };
  facilities: TFacility[];
  timezone: string;
};
