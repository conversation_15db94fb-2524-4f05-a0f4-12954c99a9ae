export type TReservation = {
  id: number,
  created: string,
  updated: string,
  class_id: number,
  ref_type: number,
  user_id: number,
  trainer_id: number,
  start_time: string,
  end_time: string,
  active: number,
  deleted: number,
  checkin: number,
  checked_in_at: string,
  class_is_sgt: number | null,
  has_feedback: number,
  is_member: number,
  is_virtual: number,
  membership_id: number,
  paid_session_id: number | number,
  decrements_sync_id: string | number,
  attending_persons: number
  cancelled_on: string | null,
  cancellation_source: string | null,
  source: string,
  added_by: number,
  notes: string | null,
  equ_id: number | null,
  meta: string | null,
  schedule_id: number | null,
  child_name: string | null,
  child_age: number | null,
  trainer_pay_amount: number | null,
  class_name: string | null,
  equipment_name: string | null,
  equipment_type_id: number | null,
  class_category_id: number | null,
  images: string[]
  instructor_first_name: string,
	instructor_last_name: string,
  room_name: string,
  gym_name: string
}

export type TUpcomingClass = {
  id: number,
  gym_id: number,
  start_time: string,
  end_time: string,
  name: string,
  description: string | null,
  allow_reservation_date: string | null,
  advance_time: number,
  spots: number,
  walkin_spots: number,
  is_sgt: number,
  class_category_id: number,
  gym_name: string | null,
  room_name: string,
  slot_id: number,
  instructor_id: number,
  instructor_first_name: string | null,
  instructor_last_name: string | null,
  facility_closed: boolean,
  cancelled: boolean,
  allow_reservations: boolean,
  instructor: string | null,
  is_class_subbed: boolean,
  subbing_instructor: string | null,
  reservation_count: number,
  spots_available: number,
  occupancy_total: number,
  images: string[],
}