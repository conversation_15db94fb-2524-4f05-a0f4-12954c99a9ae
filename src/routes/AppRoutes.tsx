import { Route, Routes } from 'react-router-dom';
import Home from '../pages/Home';
import BarcodeInputScan from '../pages/BarcodeInputScan';
import NameSearch from '../pages/NameSearch';
import Reservations from '../pages/Reservations';
import NotFound from '../pages/404/NotFound';
import Invalid from '../pages/Invalid';

function AppRoutes() {
  return (
    <Routes>
      <Route path='/invalid' element={<Invalid/>} />

      <Route path="/:uniId" element={<Home />} />
      <Route path="/barcodeinput/:uniId" element={<BarcodeInputScan />} />
      <Route path="/namesearch/:uniId" element={<NameSearch />} />
      <Route path="/reservations/:uniId" element={<Reservations />} />

      <Route path='*' element={<NotFound/>} />
    </Routes>
  );
}

export default AppRoutes