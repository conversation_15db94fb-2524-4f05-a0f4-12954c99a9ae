import { createSlice } from '@reduxjs/toolkit'
import { TConfigState } from '../types/TConfigState'
import { CLIENT_CONFIG_STORE_KEY, FACILITY_STORE_KEY, getLocalStorageItem, setLocalStorageItem } from '../../utils/LocalStorage'

const initialState: TConfigState = {
  selectedFacility: (() => {
    const localStorageData = getLocalStorageItem(FACILITY_STORE_KEY)
    if (!localStorageData) {
      return null
    }
    const parsedLocalStorageData = JSON.parse(localStorageData)
    return parsedLocalStorageData
  })(),
  clientConfig: (() => {
    const localStorageData = getLocalStorageItem(CLIENT_CONFIG_STORE_KEY)
    if (!localStorageData) {
      return null
    }
    const parsedLocalStorageData = JSON.parse(localStorageData)
    return parsedLocalStorageData
  })(),
}

export const configSlice = createSlice({
  name: 'config',
  initialState,
  reducers: {
    setClientConfig: (state, action) => {
      state.clientConfig = {
        [action.payload.uniId]: action.payload.clientConfig
      }
      setLocalStorageItem(CLIENT_CONFIG_STORE_KEY, JSON.stringify(state.clientConfig))
    },
    setSelectedFacility: (state, action) => {
      state.selectedFacility = {
        [action.payload.uniId]: action.payload.selectedFacility
      }
      setLocalStorageItem(FACILITY_STORE_KEY, JSON.stringify(state.selectedFacility))
    }
  }
})

export const {
  setClientConfig,
  setSelectedFacility
} = configSlice.actions

export default configSlice.reducer